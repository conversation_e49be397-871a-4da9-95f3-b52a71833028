<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR功能测试</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .test-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            margin: 10px;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            background: #4facfe;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #3d8bfe;
        }
        .test-info {
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <h2>OCR功能测试</h2>
        
        <div class="test-info">
            <h3>测试1: 去除换行符功能</h3>
            <p>1. 点击"模拟OCR识别"按钮</p>
            <p>2. 勾选"去除换行符"复选框，观察文本变化</p>
            <p>3. 取消勾选"去除换行符"复选框，观察是否恢复原样</p>
            <button class="test-button" onclick="simulateOCRResult()">模拟OCR识别</button>
        </div>
        
        <div class="test-info">
            <h3>测试2: 文件选择功能</h3>
            <p>1. 点击"选择图片识别"按钮，选择一张图片</p>
            <p>2. 识别完成后，再次点击"选择图片识别"按钮</p>
            <p>3. 选择同一张图片，观察是否能正常识别</p>
        </div>
        
        <div class="test-info">
            <h3>测试3: 重新识别功能</h3>
            <p>1. 完成一次识别后，观察是否出现"重新识别"按钮</p>
            <p>2. 点击"重新识别"按钮，观察是否能重新处理</p>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="main-view" class="view">
        <div class="header">
            <h1>OCR文字识别</h1>
            <button id="config-btn" class="config-btn">⚙️ 配置</button>
        </div>
        
        <div class="action-buttons">
            <button id="screenshot-btn" class="action-btn primary">
                📷 屏幕截图识别
            </button>
            <button id="upload-btn" class="action-btn">
                📁 选择图片识别
            </button>
        </div>
        
        <div id="result-container" class="result-container" style="display: none;">
            <div class="result-header">
                <h3>识别结果</h3>
                <div class="result-actions">
                    <label class="checkbox-label">
                        <input type="checkbox" id="remove-linebreaks"> 去除换行符
                    </label>
                    <button id="copy-btn" class="btn-small">复制</button>
                    <button id="clear-btn" class="btn-small">清空</button>
                </div>
            </div>
            <textarea id="result-text" class="result-text" placeholder="识别结果将显示在这里，您可以直接编辑..."></textarea>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>正在识别中...</p>
        </div>
    </div>
    
    <input type="file" id="file-input" accept="image/*" style="display: none;">

    <!-- 模拟uTools API -->
    <script>
        // 模拟uTools API
        window.ocrAPI = {
            copyText: (text) => {
                console.log('复制文本:', text);
                navigator.clipboard?.writeText(text);
            },
            hideMainWindow: () => console.log('隐藏主窗口'),
            screenCapture: (callback) => {
                console.log('模拟截图');
                setTimeout(() => {
                    // 模拟截图成功
                    callback('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
                }, 1000);
            },
            db: {
                get: (id) => null,
                put: (doc) => ({ ok: true }),
                remove: (id) => ({ ok: true })
            }
        };

        // 模拟OCR识别结果
        function simulateOCRResult() {
            const multiLineText = `这是第一行文字
这是第二行文字
这是第三行文字

这是第五行文字（前面有空行）
最后一行文字`;
            
            if (window.ocrPlugin && window.ocrPlugin.uiManager) {
                window.ocrPlugin.uiManager.showResult(multiLineText, 0.95);
                window.ocrPlugin.showReRecognizeButton();
            }
        }
    </script>

    <!-- 引入所有模块 -->
    <script src="src/config.js"></script>
    <script src="src/ocr-services.js"></script>
    <script src="src/ui.js"></script>
    <script src="src/model-manager.js"></script>
    <script src="src/main.js"></script>

    <script>
        // 页面加载完成后的测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('OCR插件已加载:', !!window.ocrPlugin);
                console.log('UI管理器已加载:', !!window.ocrPlugin?.uiManager);
            }, 1000);
        });
    </script>
</body>
</html>
